package events

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"log/slog"
	"server/server"
	"server/server/database"
	"server/server/events"
	"server/server/user"
	"strings"
	"time"
)

// EventListSubCommand lists all events
type EventListSubCommand struct {
	List cmd.SubCommand `cmd:"list"`
}

// EventStartSubCommand starts an event
type EventStartSubCommand struct {
	Start     cmd.SubCommand `cmd:"start"`
	EventType string         `cmd:"event_type"`
}

// EventStopSubCommand stops an event
type EventStopSubCommand struct {
	Stop    cmd.SubCommand `cmd:"stop"`
	EventID string         `cmd:"event_id"`
}

// EventInfoSubCommand shows information about events
type EventInfoSubCommand struct {
	Info    cmd.SubCommand        `cmd:"info"`
	EventID cmd.Optional[string] `cmd:"event_id"`
}

// EventCreateSubCommand creates a new event
type EventCreateSubCommand struct {
	Create    cmd.SubCommand    `cmd:"create"`
	EventType string            `cmd:"event_type"`
	Duration  cmd.Optional[int] `cmd:"duration_minutes"`
}

// EventScoreSubCommand shows event scores
type EventScoreSubCommand struct {
	Score      cmd.SubCommand        `cmd:"score"`
	PlayerName cmd.Optional[string] `cmd:"player_name"`
}

// EventTestSubCommand creates and starts a test event
type EventTestSubCommand struct {
	Test     cmd.SubCommand    `cmd:"test"`
	Duration cmd.Optional[int] `cmd:"duration_minutes"`
}

// Run executes the list subcommand
func (e EventListSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if !hasEventPermission(src) {
		o.Error(text.Colourf("<red>You don't have permission to use this command.</red>"))
		return
	}

	allEvents := events.GetManager().GetAllEvents()
	activeEvents := events.GetManager().GetActiveEvents()

	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))
	o.Printf(text.Colourf("<yellow>📋 Event System Status</yellow>"))
	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))
	o.Printf(text.Colourf("<dark_gray>Total Events: <white>%d</white></dark_gray>", len(allEvents)))
	o.Printf(text.Colourf("<dark_gray>Active Events: <green>%d</green></dark_gray>", len(activeEvents)))
	o.Printf("")

	if len(activeEvents) > 0 {
		o.Printf(text.Colourf("<green>🟢 Active Events:</green>"))
		for _, event := range activeEvents {
			remaining := event.RemainingTime()
			remainingStr := "∞"
			if remaining > 0 {
				remainingStr = formatDuration(remaining)
			}
			o.Printf(text.Colourf("  <yellow>• %s</yellow> <dark_gray>(ID: %s, Time Left: %s)</dark_gray>",
				event.Name(), event.ID()[:8], remainingStr))
		}
		o.Printf("")
	}

	if len(allEvents) > len(activeEvents) {
		o.Printf(text.Colourf("<dark_gray>⚪ Inactive Events:</dark_gray>"))
		for _, event := range allEvents {
			if event.Status() != events.EventStatusActive {
				o.Printf(text.Colourf("  <dark_gray>• %s</dark_gray> <dark_gray>(ID: %s, Status: %s)</dark_gray>",
					event.Name(), event.ID()[:8], event.Status()))
			}
		}
	}
}

// Run executes the start subcommand
func (e EventStartSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if !hasEventPermission(src) {
		o.Error(text.Colourf("<red>You don't have permission to use this command.</red>"))
		return
	}

	// Find event by type or ID
	var targetEvent events.Event
	allEvents := events.GetManager().GetAllEvents()
	
	for _, event := range allEvents {
		if string(event.Type()) == e.EventType || event.ID() == e.EventType || 
		   strings.Contains(strings.ToLower(event.Name()), strings.ToLower(e.EventType)) {
			targetEvent = event
			break
		}
	}

	if targetEvent == nil {
		o.Error(text.Colourf("<red>Event not found: %s</red>", e.EventType))
		o.Printf(text.Colourf("<dark_gray>Available events: %s</dark_gray>", getAvailableEventTypes()))
		return
	}

	if err := events.GetManager().StartEvent(targetEvent.ID()); err != nil {
		o.Error(text.Colourf("<red>Failed to start event: %s</red>", err.Error()))
		return
	}

	o.Printf(text.Colourf("<green>✅ Event started successfully!</green>"))
	o.Printf(text.Colourf("<yellow>Event: %s</yellow>", targetEvent.Name()))
	o.Printf(text.Colourf("<dark_gray>ID: %s</dark_gray>", targetEvent.ID()))
	
	// Broadcast to all players
	broadcastEventMessage(fmt.Sprintf("🎉 %s has started!", targetEvent.Name()))
}

// Run executes the stop subcommand
func (e EventStopSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if !hasEventPermission(src) {
		o.Error(text.Colourf("<red>You don't have permission to use this command.</red>"))
		return
	}

	// Find event by ID or partial match
	var targetEvent events.Event
	allEvents := events.GetManager().GetAllEvents()
	
	for _, event := range allEvents {
		if event.ID() == e.EventID || strings.HasPrefix(event.ID(), e.EventID) {
			targetEvent = event
			break
		}
	}

	if targetEvent == nil {
		o.Error(text.Colourf("<red>Event not found: %s</red>", e.EventID))
		return
	}

	if err := events.GetManager().StopEvent(targetEvent.ID()); err != nil {
		o.Error(text.Colourf("<red>Failed to stop event: %s</red>", err.Error()))
		return
	}

	o.Printf(text.Colourf("<green>✅ Event stopped successfully!</green>"))
	o.Printf(text.Colourf("<yellow>Event: %s</yellow>", targetEvent.Name()))
	
	// Broadcast to all players
	broadcastEventMessage(fmt.Sprintf("⏰ %s has ended!", targetEvent.Name()))
}

// Run executes the info subcommand
func (e EventInfoSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	eventID, _ := e.EventID.Load()
	if eventID == "" {
		// Show general event info
		activeEvents := events.GetManager().GetActiveEvents()
		if len(activeEvents) == 0 {
			o.Printf(text.Colourf("<yellow>No events are currently active.</yellow>"))
			return
		}

		for _, event := range activeEvents {
			showEventInfo(event, o)
		}
		return
	}

	// Show specific event info
	var targetEvent events.Event
	allEvents := events.GetManager().GetAllEvents()
	
	for _, event := range allEvents {
		if event.ID() == eventID || strings.HasPrefix(event.ID(), eventID) {
			targetEvent = event
			break
		}
	}

	if targetEvent == nil {
		o.Error(text.Colourf("<red>Event not found: %s</red>", eventID))
		return
	}

	showEventInfo(targetEvent, o)
}

// Run executes the create subcommand
func (e EventCreateSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if !hasEventPermission(src) {
		o.Error(text.Colourf("<red>You don't have permission to use this command.</red>"))
		return
	}

	eventType := events.EventType(e.EventType)
	
	// Get default config
	configManager := events.GetConfigManager()
	if configManager == nil {
		o.Error(text.Colourf("<red>Configuration manager not available.</red>"))
		return
	}

	config, exists := configManager.GetConfig(eventType)
	if !exists {
		o.Error(text.Colourf("<red>Unknown event type: %s</red>", e.EventType))
		o.Printf(text.Colourf("<dark_gray>Available types: %s</dark_gray>", getAvailableEventTypes()))
		return
	}

	// Override duration if specified
	duration, _ := e.Duration.Load()
	if duration > 0 {
		config.Duration = time.Duration(duration) * time.Minute
	}

	// Create the event
	event, err := events.GetFactory().CreateEvent(eventType, config)
	if err != nil {
		o.Error(text.Colourf("<red>Failed to create event: %s</red>", err.Error()))
		return
	}

	// Register the event
	if err := events.GetManager().RegisterEvent(event); err != nil {
		o.Error(text.Colourf("<red>Failed to register event: %s</red>", err.Error()))
		return
	}

	o.Printf(text.Colourf("<green>✅ Event created successfully!</green>"))
	o.Printf(text.Colourf("<yellow>Event: %s</yellow>", event.Name()))
	o.Printf(text.Colourf("<dark_gray>ID: %s</dark_gray>", event.ID()))
	o.Printf(text.Colourf("<dark_gray>Type: %s</dark_gray>", event.Type()))
}

// Run executes the score subcommand
func (e EventScoreSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	var targetPlayer *player.Player
	
	playerName, _ := e.PlayerName.Load()
	if playerName != "" {
		// Find player by name
		for pl := range server.MCServer.Players(tx) {
			if strings.EqualFold(pl.Name(), playerName) {
				targetPlayer = pl
				break
			}
		}
		
		if targetPlayer == nil {
			o.Error(text.Colourf("<red>Player not found: %s</red>", playerName))
			return
		}
	} else {
		// Use command sender if it's a player
		if pl, ok := src.(*player.Player); ok {
			targetPlayer = pl
		} else {
			o.Error(text.Colourf("<red>You must specify a player name when using this command from console.</red>"))
			return
		}
	}

	u := user.GetUser(targetPlayer)
	activeEvents := events.GetManager().GetActiveEvents()

	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))
	o.Printf(text.Colourf("<yellow>📊 Event Scores for %s</yellow>", targetPlayer.Name()))
	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))

	if len(activeEvents) == 0 {
		o.Printf(text.Colourf("<dark_gray>No active events.</dark_gray>"))
		return
	}

	for _, event := range activeEvents {
		score := 0
		if u.Data.Faction.Stats.EventScores != nil {
			if eventScore, exists := u.Data.Faction.Stats.EventScores[event.ID()]; exists {
				score = eventScore
			}
		}
		
		o.Printf(text.Colourf("<yellow>%s:</yellow> <green>%d points</green>", event.Name(), score))
		
		// Show additional stats for lucky block events
		if event.Type() == events.EventTypeLuckyBlock {
			if lbe, ok := event.(*events.LuckyBlockEvent); ok {
				if stats := lbe.GetLuckyBlockStats(targetPlayer.UUID().String()); stats != nil {
					o.Printf(text.Colourf("  <dark_gray>• Blocks Mined: %d</dark_gray>", stats.BlocksMined))
					o.Printf(text.Colourf("  <dark_gray>• Sponges Found: %d</dark_gray>", stats.SpongesFound))
					o.Printf(text.Colourf("  <dark_gray>• Rewards Gained: %d</dark_gray>", len(stats.RewardsGained)))
				}
			}
		}
	}
}

// Run executes the test subcommand
func (e EventTestSubCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if !hasEventPermission(src) {
		o.Error(text.Colourf("<red>You don't have permission to use this command.</red>"))
		return
	}

	// Get duration (default 5 minutes for testing)
	duration, _ := e.Duration.Load()
	if duration <= 0 {
		duration = 5
	}

	// Create test event configuration
	config := events.EventConfig{
		Enabled:     true,
		Duration:    time.Duration(duration) * time.Minute,
		MaxPlayers:  50,
		MinPlayers:  1,
		AutoStart:   true,
		AutoRestart: false,
		CustomSettings: map[string]interface{}{
			"sponge_chance":  0.15,  // 15% chance for sponge
			"reward_chance":  0.8,   // 80% chance for reward from sponge
			"spawn_radius":   5.0,   // 5 block radius for sponge spawn
		},
	}

	// Create the lucky block event
	event, err := events.GetFactory().CreateEvent(events.EventTypeLuckyBlock, config)
	if err != nil {
		o.Error(text.Colourf("<red>Failed to create test event: %s</red>", err.Error()))
		return
	}

	// Register the event
	if err := events.GetManager().RegisterEvent(event); err != nil {
		o.Error(text.Colourf("<red>Failed to register test event: %s</red>", err.Error()))
		return
	}

	// Start the event immediately
	if err := events.GetManager().StartEvent(event.ID()); err != nil {
		o.Error(text.Colourf("<red>Failed to start test event: %s</red>", err.Error()))
		return
	}

	o.Printf(text.Colourf("<green>✅ Test event created and started!</green>"))
	o.Printf(text.Colourf("<yellow>Event: %s</yellow>", event.Name()))
	o.Printf(text.Colourf("<dark_gray>ID: %s</dark_gray>", event.ID()))
	o.Printf(text.Colourf("<dark_gray>Duration: %d minutes</dark_gray>", duration))
	o.Printf(text.Colourf("<dark_gray>Sponge Chance: 15%</dark_gray>"))
	o.Printf(text.Colourf("<dark_gray>Reward Chance: 80%</dark_gray>"))
	o.Printf("")
	o.Printf(text.Colourf("<aqua>🎮 How to test:</aqua>"))
	o.Printf(text.Colourf("<white>1. Mine any block to trigger lucky block event</white>"))
	o.Printf(text.Colourf("<white>2. Look for sponge blocks that spawn nearby</white>"))
	o.Printf(text.Colourf("<white>3. Mine sponges to get rewards and score</white>"))
	o.Printf(text.Colourf("<white>4. Check your score with: /event score</white>"))

	// Broadcast to all players
	broadcastEventMessage(fmt.Sprintf("🧪 Test Event Started! %s (ID: %s) is now active for %d minutes!", event.Name(), event.ID(), duration))
}

// Helper functions

func hasEventPermission(src cmd.Source) bool {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		return u.Data.Rank() <= database.Moderator
	}
	return true // Console always has permission
}

func getAvailableEventTypes() string {
	factory := events.GetFactory()
	if factory == nil {
		return "none"
	}
	
	types := factory.GetSupportedTypes()
	typeStrings := make([]string, len(types))
	for i, t := range types {
		typeStrings[i] = string(t)
	}
	return strings.Join(typeStrings, ", ")
}

func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%ds", int(d.Seconds()))
	} else if d < time.Hour {
		return fmt.Sprintf("%dm", int(d.Minutes()))
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) % 60
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
}

func showEventInfo(event events.Event, o *cmd.Output) {
	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))
	o.Printf(text.Colourf("<yellow>📋 %s</yellow>", event.Name()))
	o.Printf(text.Colourf("<gold>═══════════════════════════════════</gold>"))
	o.Printf(text.Colourf("<dark_gray>ID:</dark_gray> <white>%s</white>", event.ID()))
	o.Printf(text.Colourf("<dark_gray>Type:</dark_gray> <white>%s</white>", event.Type()))
	o.Printf(text.Colourf("<dark_gray>Status:</dark_gray> <white>%s</white>", event.Status()))
	o.Printf(text.Colourf("<dark_gray>Priority:</dark_gray> <white>%d</white>", event.Priority()))

	if !event.StartTime().IsZero() {
		o.Printf(text.Colourf("<dark_gray>Started:</dark_gray> <white>%s</white>", event.StartTime().Format("15:04:05")))
	}

	if event.RemainingTime() > 0 {
		o.Printf(text.Colourf("<dark_gray>Time Remaining:</dark_gray> <green>%s</green>", formatDuration(event.RemainingTime())))
	}

	o.Printf(text.Colourf("<dark_gray>Description:</dark_gray> <white>%s</white>", event.Description()))
	o.Printf("")
}

func broadcastEventMessage(message string) {
	// Broadcast to all online players
	if server.MCServer != nil {
		// Get all online players and send them the message
		for pl := range server.MCServer.Players(nil) {
			pl.Message(fmt.Sprintf("§6[EVENT] §e%s", message))
		}
	}

	// Also log it
	slog.Info("Event broadcast", "message", message)
}
