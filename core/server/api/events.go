package api

import (
	"net/http"
	"server/server/events"
	"time"

	"github.com/gin-gonic/gin"
)

// EventResponse represents the API response for event data
type EventResponse struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      string                 `json:"status"`
	Priority    int                    `json:"priority"`
	StartTime   *time.Time             `json:"start_time,omitempty"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    *int64                 `json:"duration,omitempty"` // Duration in seconds
	Remaining   *int64                 `json:"remaining,omitempty"` // Remaining time in seconds
	Config      events.EventConfig     `json:"config"`
	State       map[string]interface{} `json:"state"`
}

// ParticipantResponse represents the API response for participant data
type ParticipantResponse struct {
	PlayerUUID string                 `json:"player_uuid"`
	PlayerName string                 `json:"player_name"`
	JoinTime   time.Time              `json:"join_time"`
	Score      int                    `json:"score"`
	Rewards    []events.RewardInstance `json:"rewards"`
	Stats      map[string]interface{} `json:"stats"`
}

// CreateEventRequest represents the request to create a new event
type CreateEventRequest struct {
	Type   string             `json:"type" binding:"required"`
	Config events.EventConfig `json:"config"`
}

// UpdateEventRequest represents the request to update an event
type UpdateEventRequest struct {
	Config *events.EventConfig `json:"config,omitempty"`
	State  map[string]interface{} `json:"state,omitempty"`
}

// initEventRequests initializes event-related API endpoints
func initEventRequests(rg *gin.RouterGroup) {
	// Event management endpoints
	rg.GET("/events", listEvents)
	rg.GET("/events/active", listActiveEvents)
	rg.GET("/events/:id", getEvent)
	rg.POST("/events", createEvent)
	rg.PUT("/events/:id", updateEvent)
	rg.DELETE("/events/:id", deleteEvent)
	
	// Event control endpoints
	rg.POST("/events/:id/start", startEvent)
	rg.POST("/events/:id/stop", stopEvent)
	rg.POST("/events/:id/pause", pauseEvent)
	rg.POST("/events/:id/resume", resumeEvent)
	rg.POST("/events/:id/restart", restartEvent)
	
	// Participant endpoints
	rg.GET("/events/:id/participants", getEventParticipants)
	rg.GET("/events/:id/participants/:player_uuid", getEventParticipant)
	// TODO: Implement these endpoints
	// rg.POST("/events/:id/participants/:player_uuid", addEventParticipant)

	// Monitoring and health endpoints
	rg.GET("/events/health", getEventSystemHealth)
	rg.GET("/events/stats", getEventSystemStats)
	rg.GET("/events/persistence/stats", getPersistenceStats)
	// rg.DELETE("/events/:id/participants/:player_uuid", removeEventParticipant)

	// Statistics endpoints
	// TODO: Implement these endpoints
	// rg.GET("/events/:id/stats", getEventStats)
	// rg.GET("/events/:id/leaderboard", getEventLeaderboard)
}

// listEvents returns all events
func listEvents(c *gin.Context) {
	allEvents := events.GetManager().GetAllEvents()
	response := make([]EventResponse, len(allEvents))
	
	for i, event := range allEvents {
		response[i] = eventToResponse(event)
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"count":   len(response),
	})
}

// listActiveEvents returns all active events
func listActiveEvents(c *gin.Context) {
	activeEvents := events.GetManager().GetActiveEvents()
	response := make([]EventResponse, len(activeEvents))
	
	for i, event := range activeEvents {
		response[i] = eventToResponse(event)
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"count":   len(response),
	})
}

// getEvent returns a specific event by ID
func getEvent(c *gin.Context) {
	eventID := c.Param("id")
	event := events.GetManager().GetEvent(eventID)
	
	if event == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Event not found",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    eventToResponse(event),
	})
}

// createEvent creates a new event
func createEvent(c *gin.Context) {
	var req CreateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}
	
	eventType := events.EventType(req.Type)
	event, err := events.GetFactory().CreateEvent(eventType, req.Config)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create event: " + err.Error(),
		})
		return
	}
	
	if err := events.GetManager().RegisterEvent(event); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to register event: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    eventToResponse(event),
	})
}

// updateEvent updates an existing event
func updateEvent(c *gin.Context) {
	eventID := c.Param("id")
	event := events.GetManager().GetEvent(eventID)
	
	if event == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Event not found",
		})
		return
	}
	
	var req UpdateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}
	
	if req.Config != nil {
		if err := event.SetConfig(*req.Config); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to update config: " + err.Error(),
			})
			return
		}
	}
	
	if req.State != nil {
		for key, value := range req.State {
			event.SetState(key, value)
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    eventToResponse(event),
	})
}

// deleteEvent deletes an event
func deleteEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().UnregisterEvent(eventID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event deleted successfully",
	})
}

// startEvent starts an event
func startEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().StartEvent(eventID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	event := events.GetManager().GetEvent(eventID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event started successfully",
		"data":    eventToResponse(event),
	})
}

// stopEvent stops an event
func stopEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().StopEvent(eventID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	event := events.GetManager().GetEvent(eventID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event stopped successfully",
		"data":    eventToResponse(event),
	})
}

// pauseEvent pauses an event
func pauseEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().PauseEvent(eventID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	event := events.GetManager().GetEvent(eventID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event paused successfully",
		"data":    eventToResponse(event),
	})
}

// resumeEvent resumes an event
func resumeEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().ResumeEvent(eventID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	event := events.GetManager().GetEvent(eventID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event resumed successfully",
		"data":    eventToResponse(event),
	})
}

// restartEvent restarts an event
func restartEvent(c *gin.Context) {
	eventID := c.Param("id")
	
	if err := events.GetManager().RestartEvent(eventID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	event := events.GetManager().GetEvent(eventID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Event restarted successfully",
		"data":    eventToResponse(event),
	})
}

// getEventParticipants returns all participants of an event
func getEventParticipants(c *gin.Context) {
	eventID := c.Param("id")
	participants := events.GetManager().GetParticipants(eventID)
	
	if participants == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Event not found",
		})
		return
	}
	
	response := make([]ParticipantResponse, len(participants))
	for i, participant := range participants {
		response[i] = ParticipantResponse{
			PlayerUUID: participant.PlayerUUID,
			PlayerName: participant.PlayerName,
			JoinTime:   participant.JoinTime,
			Score:      participant.Score,
			Rewards:    participant.Rewards,
			Stats:      participant.Stats,
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"count":   len(response),
	})
}

// getEventParticipant returns a specific participant
func getEventParticipant(c *gin.Context) {
	eventID := c.Param("id")
	playerUUID := c.Param("player_uuid")
	
	participants := events.GetManager().GetParticipants(eventID)
	if participants == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Event not found",
		})
		return
	}
	
	for _, participant := range participants {
		if participant.PlayerUUID == playerUUID {
			response := ParticipantResponse{
				PlayerUUID: participant.PlayerUUID,
				PlayerName: participant.PlayerName,
				JoinTime:   participant.JoinTime,
				Score:      participant.Score,
				Rewards:    participant.Rewards,
				Stats:      participant.Stats,
			}
			
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    response,
			})
			return
		}
	}
	
	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"error":   "Participant not found",
	})
}

// Helper function to convert Event to EventResponse
func eventToResponse(event events.Event) EventResponse {
	response := EventResponse{
		ID:          event.ID(),
		Type:        string(event.Type()),
		Name:        event.Name(),
		Description: event.Description(),
		Status:      string(event.Status()),
		Priority:    int(event.Priority()),
		Config:      event.Config(),
		State:       event.State(),
	}
	
	if !event.StartTime().IsZero() {
		startTime := event.StartTime()
		response.StartTime = &startTime
	}

	if !event.EndTime().IsZero() {
		endTime := event.EndTime()
		response.EndTime = &endTime
	}
	
	if event.Duration() > 0 {
		duration := int64(event.Duration().Seconds())
		response.Duration = &duration
	}
	
	if event.RemainingTime() > 0 {
		remaining := int64(event.RemainingTime().Seconds())
		response.Remaining = &remaining
	}
	
	return response
}

// getEventSystemHealth returns the health status of the event system
func getEventSystemHealth(c *gin.Context) {
	healthStatus := events.GetManager().GetHealthStatus()

	// Determine HTTP status based on overall health
	httpStatus := http.StatusOK
	if overallStatus, ok := healthStatus["overall_status"].(events.HealthStatus); ok {
		switch overallStatus {
		case events.HealthStatusUnhealthy:
			httpStatus = http.StatusServiceUnavailable
		case events.HealthStatusDegraded:
			httpStatus = http.StatusPartialContent
		}
	}

	c.JSON(httpStatus, gin.H{
		"success": true,
		"data":    healthStatus,
	})
}

// getEventSystemStats returns general statistics about the event system
func getEventSystemStats(c *gin.Context) {
	manager := events.GetManager()
	allEvents := manager.GetAllEvents()
	activeEvents := manager.GetActiveEvents()

	// Calculate statistics
	eventsByType := make(map[string]int)
	eventsByStatus := make(map[string]int)

	for _, event := range allEvents {
		eventsByType[string(event.Type())]++
		eventsByStatus[string(event.Status())]++
	}

	stats := map[string]interface{}{
		"total_events":     len(allEvents),
		"active_events":    len(activeEvents),
		"events_by_type":   eventsByType,
		"events_by_status": eventsByStatus,
		"timestamp":        time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// getPersistenceStats returns persistence system statistics
func getPersistenceStats(c *gin.Context) {
	persistenceStats := events.GetManager().GetPersistenceStats()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    persistenceStats,
	})
}
